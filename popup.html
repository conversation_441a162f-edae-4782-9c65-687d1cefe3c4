<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'self';">
  <title>reCAPTCHA Audio Solver</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 id="title" data-lang-key="title">reCAPTCHA Audio Solver</h1>
      <div class="toggle-container">
        <label class="switch">
          <input type="checkbox" id="solverToggle">
          <span class="slider round"></span>
        </label>
        <span id="toggleStatus" data-lang-key="toggleInactive"></span>
      </div>
    </div>

    <div class="status-container loading">
      <div id="statusLabel" class="status-label" data-lang-key="statusLabel"></div>
      <div id="status" class="status-value" data-lang-key="loading"></div>
    </div>

    <div class="footer">
      <div class="version">v3.8.2</div>
      <div class="author" data-lang-key="authorCredit"></div>
    </div>

    <div class="bottom-container">
      <div class="lang-selector">
        <button id="langEN" class="lang-btn">EN</button>
        <button id="langID" class="lang-btn">ID</button>
        <button id="langES" class="lang-btn">ES</button>
      </div>
      <div class="theme-toggle" id="themeToggle">
        <svg class="theme-icon" id="themeIcon" viewBox="0 0 24 24">
          <!-- Icon path will be set by JS -->
        </svg>
      </div>
    </div>
  </div>
  <script src="constants.js"></script>
  <script src="lang.js"></script>
  <script src="popup.js"></script>
</body>
</html>
