'use strict';

/**
 * reCAPTCHA Audio Solver v3.8.2
 * Popup script - Refactored for clarity and maintainability
 */

document.addEventListener('DOMContentLoaded', () => {
  // --- Element Cache - Optimized to only cache existing elements ---
  const ui = {
    solverToggle: document.getElementById('solverToggle'),
    toggleStatus: document.getElementById('toggleStatus'),
    status: document.getElementById('status'),
    statusContainer: document.querySelector('.status-container'),
    themeToggle: document.getElementById('themeToggle'),
    themeIcon: document.getElementById('themeIcon'),
    langEN: document.getElementById('langEN'),
    langID: document.getElementById('langID'),
    langES: document.getElementById('langES'),
  };

  // Validate critical UI elements
  const criticalElements = ['solverToggle', 'status', 'statusContainer'];
  const missingElements = criticalElements.filter(id => !ui[id]);
  if (missingElements.length > 0) {
    console.error('[reCAPTCHA Audio Solver] Missing critical UI elements:', missingElements);
    return; // Exit early if critical elements are missing
  }



  function fadeUpdate(element, text) {
    // Validate element and text
    if (!element || !element.style || element.textContent === text) return;

    element.style.opacity = '0';
    setTimeout(() => {
      // Double-check element still exists
      if (element && element.textContent !== undefined) {
        element.textContent = text;
        element.style.opacity = '1';
      }
    }, 150);
  }

  // --- UI Update Function ---
  function updateUI(data) {
    // Validate data parameter
    if (!data || typeof data !== 'object') {
      console.error('[reCAPTCHA Audio Solver] Invalid data provided to updateUI');
      return;
    }

    try {
      // Update toggle and main status
      if (ui.solverToggle) {
        ui.solverToggle.checked = Boolean(data.enabled);
      }

      let statusKey = data.enabled ? 'active' : 'inactive';
      let statusClass = data.enabled ? 'active' : 'inactive';

      // Handle different status states with better detection
      if (data.botDetected && data.lastStatus &&
          (data.lastStatus.includes('Bot terdeteksi') || data.lastStatus.includes('Bot detected'))) {
        statusKey = 'botDetected';
        statusClass = 'bot-detected';
      } else if (data.lastStatus &&
                 (data.lastStatus.includes('Processing') ||
                  data.lastStatus.includes('Memproses') ||
                  data.lastStatus.includes('Procesando'))) {
        statusKey = 'processing';
        statusClass = 'processing';
      }

      fadeUpdate(ui.status, t(statusKey));
      ui.toggleStatus.textContent = t(data.enabled ? 'toggleActive' : 'toggleInactive');

      // Update container style
      ui.statusContainer.className = `status-container ${statusClass}`;

    } catch (error) {
      console.error('[reCAPTCHA Audio Solver] Error updating UI:', error.message);
    }
  }

  // --- Language & Theme ---
  function applyTranslations() {
    document.querySelectorAll('[data-lang-key]').forEach(el => {
      el.textContent = t(el.dataset.langKey);
    });
    // Re-apply dynamic text
    chrome.storage.local.get(['enabled', 'botDetected'], (data) => {
      if (chrome.runtime.lastError) {
        console.error('[reCAPTCHA Audio Solver] Storage error in applyTranslations:', chrome.runtime.lastError.message || 'Unknown storage error');
        return;
      }
      ui.toggleStatus.textContent = t(data.enabled ? 'toggleActive' : 'toggleInactive');
      const statusKey = data.botDetected ? 'botDetected' : (data.enabled ? 'active' : 'inactive');
      ui.status.textContent = t(statusKey);
    });
  }

  function setupLanguageSwitcher() {
    const buttons = { en: ui.langEN, id: ui.langID, es: ui.langES };
    const currentLang = getLanguage();

    Object.values(buttons).forEach(btn => btn.classList.remove('lang-active'));
    if(buttons[currentLang]) buttons[currentLang].classList.add('lang-active');

    Object.entries(buttons).forEach(([lang, button]) => {
      button.addEventListener('click', () => {
        if (getLanguage() === lang) return;
        setLanguage(lang);
        Object.values(buttons).forEach(b => b.classList.remove('lang-active'));
        button.classList.add('lang-active');
        applyTranslations();
      });
    });
  }

  function setupTheme() {
    const lightIcon = '<path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z" />';
    const darkIcon = '<path d="M12,18C11.11,18 10.26,17.8 9.5,17.45C11.56,16.5 13,14.42 13,12C13,9.58 11.56,7.5 9.5,6.55C10.26,6.2 11.11,6 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z" />';

    const applyTheme = (theme) => {
      document.body.dataset.theme = theme;
      ui.themeIcon.innerHTML = theme === 'dark' ? darkIcon : lightIcon;
      try { localStorage.setItem('reCAPTCHA_solver_theme', theme); } catch(e) {}
    };

    ui.themeToggle.addEventListener('click', () => {
      applyTheme(document.body.dataset.theme === 'dark' ? 'light' : 'dark');
    });

    applyTheme(localStorage.getItem('reCAPTCHA_solver_theme') || 'light');
  }

  // --- Real-time Updates ---
  function setupRealTimeUpdates() {
    // Listen for storage changes for immediate updates with enhanced error handling
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace !== 'local') return;

      try {
        // Check if relevant data changed
        const hasRelevantChanges = changes.lastStatus || changes.enabled || changes.botDetected;

        if (hasRelevantChanges) {
          // Log the changes for debugging
          console.debug('[reCAPTCHA Audio Solver] Storage changes detected:', Object.keys(changes));

          // Get fresh data and update UI immediately with timeout protection
          const storageTimeout = setTimeout(() => {
            console.warn('[reCAPTCHA Audio Solver] Storage read timeout in real-time update');
          }, 3000);

          chrome.storage.local.get(['enabled', 'botDetected', 'lastStatus'], (data) => {
            clearTimeout(storageTimeout);

            if (chrome.runtime.lastError) {
              console.warn('[reCAPTCHA Audio Solver] Storage error in real-time update:', chrome.runtime.lastError.message);
              return;
            }

            try {
              const fullData = {
                enabled: data.enabled !== undefined ? data.enabled : true,
                botDetected: data.botDetected || false,
                lastStatus: data.lastStatus || (data.enabled ? 'active' : 'inactive')
              };

              updateUI(fullData);
            } catch (error) {
              console.error('[reCAPTCHA Audio Solver] Error processing real-time update:', error);
            }
          });
        }
      } catch (error) {
        console.error('[reCAPTCHA Audio Solver] Error in storage change listener:', error);
      }
    });

    // Poll for updates every 2 seconds when popup is open
    const updateInterval = setInterval(() => {
      // Query active tab for real-time stats
      try {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (chrome.runtime.lastError) {
            console.warn('[reCAPTCHA Audio Solver] Tab query error:', chrome.runtime.lastError.message || 'Unknown tab query error');
            loadInitialData();
            return;
          }

          if (tabs && tabs[0] && tabs[0].id) {
            chrome.tabs.sendMessage(tabs[0].id, { action: 'getStats' }, (response) => {
              if (chrome.runtime.lastError) {
                // Content script not available, fallback to storage data
                loadInitialData();
                return;
              }

              if (response && typeof response === 'object') {
                // Update UI with real-time data from content script
                updateUI(response);
              } else {
                // Invalid response, fallback to storage data
                loadInitialData();
              }
            });
          } else {
            // No active tab, fallback to storage data
            loadInitialData();
          }
        });
      } catch (error) {
        console.error('[reCAPTCHA Audio Solver] Error in update interval:', error);
        loadInitialData();
      }
    }, 2000);

    // Clean up interval when popup closes
    window.addEventListener('beforeunload', () => {
      clearInterval(updateInterval);
    });
  }

  // --- Event Handlers ---
  function setupEventListeners() {
    ui.solverToggle.addEventListener('change', () => {
      const enabled = ui.solverToggle.checked;
      const originalState = !enabled;

      // Disable toggle temporarily to prevent rapid clicking
      ui.solverToggle.disabled = true;

      // Send message to content script for immediate toggle
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (chrome.runtime.lastError) {
          console.error('[reCAPTCHA Audio Solver] Tab query error:', chrome.runtime.lastError.message || 'Unknown tab query error');
          ui.solverToggle.checked = originalState;
          ui.solverToggle.disabled = false;
          return;
        }

        if (tabs && tabs[0] && tabs[0].id) {
          chrome.tabs.sendMessage(tabs[0].id, {
            action: 'toggleSolver',
            enabled: enabled
          }, (response) => {
            if (chrome.runtime.lastError) {
              console.warn('[reCAPTCHA Audio Solver] Content script not available');
              // Continue with storage update even if content script is not available

              // Update storage directly here instead of falling through to code that might use response
              chrome.storage.local.set({ enabled }, () => {
                if (chrome.runtime.lastError) {
                  console.error('[reCAPTCHA Audio Solver] Storage error in toggle:', chrome.runtime.lastError.message || 'Unknown storage error');
                  ui.solverToggle.checked = originalState;
                }
                ui.solverToggle.disabled = false;
              });
              return; // Add return to prevent further execution
            } else if (!response || !response.success) {
              console.error('[reCAPTCHA Audio Solver] Failed to toggle solver');
              ui.solverToggle.checked = originalState;
              ui.solverToggle.disabled = false;
              return;
            }

            // Update storage
            chrome.storage.local.set({ enabled }, () => {
              if (chrome.runtime.lastError) {
                console.error('[reCAPTCHA Audio Solver] Storage error in toggle:', chrome.runtime.lastError.message || 'Unknown storage error');
                ui.solverToggle.checked = originalState;
              }
              ui.solverToggle.disabled = false;
            });
          });
        } else {
          // No active tab, just update storage
          chrome.storage.local.set({ enabled }, () => {
            if (chrome.runtime.lastError) {
              console.error('[reCAPTCHA Audio Solver] Storage error in toggle:', chrome.runtime.lastError.message || 'Unknown storage error');
              ui.solverToggle.checked = originalState;
            }
            ui.solverToggle.disabled = false;
          });
        }
      });
    });
  }

  // --- Data Validation and Synchronization ---
  function validateAndSyncData() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Data validation timeout'));
      }, 5000);

      chrome.storage.local.get(['enabled', 'botDetected', 'lastStatus'], (data) => {
        clearTimeout(timeout);

        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message || 'Storage error'));
          return;
        }

        try {
          // Validate and sanitize data
          const validatedData = {
            enabled: data.enabled !== undefined ? data.enabled : true,
            botDetected: data.botDetected || false,
            lastStatus: (data.lastStatus && typeof data.lastStatus === 'string') ? data.lastStatus : (data.enabled ? 'active' : 'inactive')
          };

          resolve(validatedData);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  // --- Initial Load ---
  async function loadInitialData() {
    ui.statusContainer.classList.add('loading');

    try {
      const validatedData = await validateAndSyncData();
      ui.statusContainer.classList.remove('loading');
      updateUI(validatedData);
      console.debug('[reCAPTCHA Audio Solver] Initial data loaded and validated');
    } catch (error) {
      console.error('[reCAPTCHA Audio Solver] Failed to load initial data:', error);
      ui.statusContainer.classList.remove('loading');

      // Fallback to default values
      const fallbackData = {
        enabled: true,
        botDetected: false,
        lastStatus: 'active'
      };
      updateUI(fallbackData);
    }
  }

  // --- Main Execution ---
  applyTranslations();
  setupLanguageSwitcher();
  setupTheme();
  setupEventListeners();
  setupRealTimeUpdates();
  loadInitialData();
});
